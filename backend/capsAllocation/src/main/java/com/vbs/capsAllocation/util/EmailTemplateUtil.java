package com.vbs.capsAllocation.util;

import com.vbs.capsAllocation.dto.VunnoMgmtDto;
import com.vbs.capsAllocation.dto.VunnoRequestDto;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class EmailTemplateUtil {

    public String getVunnoApprovalEmail(String backupInfo, String type, String fromDate, String toDate, String duration) {
        String backupDisplay = "Work from Home".equalsIgnoreCase(type) ? "N/A (WFH)" : backupInfo;
        String message = "Work from Home".equalsIgnoreCase(type) ? "" : "Please coordinate with your backup in your absence.";

        return "<!DOCTYPE html>"
                + "<html><head>"
                + "<style>"
                + "body { font-family: Arial, sans-serif; line-height: 1.6; margin: 20px; }"
                + "h3 { color: #2c5aa0; margin-bottom: 20px; }"
                + "p { margin: 10px 0; }"
                + "hr { margin: 20px 0; border: 1px solid #ddd; }"
                + ".info-section { background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 15px 0; }"
                + ".highlight { background-color: #fff3cd; padding: 10px; border-radius: 5px; margin: 15px 0; }"
                + "</style>"
                + "</head><body>"
                + "<h3>Your " + type + " request has been approved!</h3>"
                + "<div class='info-section'>"
                + "<p><b>Duration:</b> " + duration + "</p>"
                + "<p><b>From:</b> " + fromDate + "</p>"
                + "<p><b>To:</b> " + toDate + "</p>"
                + "<p><b>Backup:</b> " + backupDisplay + "</p>"
                + "</div>"
                + (message.isEmpty() ? "" : "<hr><p>" + message + "</p>")
                + "<div class='highlight'><p><b>PLEASE IGNORE THIS MAIL</b></p></div>"
                + "</body></html>";
    }



    public String getVunnoNotificationEmail(VunnoRequestDto requestDto, VunnoMgmtDto dto, List<Double> counts) {
        return getVunnoNotificationEmail(requestDto, dto, counts, false);
    }

    public String getVunnoNotificationEmail(VunnoRequestDto requestDto, VunnoMgmtDto dto, List<Double> counts, boolean isWFHMoreThan3Days) {

        String applicationType = requestDto.getApplicationType();
        boolean isLeave = "Leave".equalsIgnoreCase(applicationType);
        boolean isDuration = "Multiple Days".equalsIgnoreCase(requestDto.getLvWfhDuration());

        String requestReason = isLeave ? requestDto.getBackupInfo() : requestDto.getReason();
        String requestFor = isLeave ? requestDto.getLeaveType() : "NA (WFH)";
        String extraSectionTitle = isLeave ? "Backup Information" : "Comment";
        String timesheetLink = isLeave ? requestDto.getTimesheetProof() : "NA";
        String oooLink = isLeave ? requestDto.getOooProof() : "NA";
        String duration = isDuration ? "Multiple Days" : requestDto.getLvWfhDuration();

        // Balances from counts
        double slBalance = counts.get(0);
        double clBalance = counts.get(1);
        double elBalance = counts.get(2);
        double totalBalance = counts.get(3);
        double totalWFHQuaterly = counts.get(5);
        double totalLeavesTakenThisQuarter = counts.size() > 4 ? counts.get(6) : 0.0;

        return "<!DOCTYPE html>"
                + "<html><head>"
                + "<base target=\"_top\">"
                + "<style>"
                + "body { font-family: Arial, sans-serif; line-height: 1.6; margin: 20px; color: #333; }"
                + "h3 { color: #2c5aa0; margin-bottom: 20px; border-bottom: 2px solid #2c5aa0; padding-bottom: 10px; }"
                + "h4 { color: #4a6741; margin-top: 25px; margin-bottom: 15px; }"
                + "p { margin: 12px 0; }"
                + "a { color: #2c5aa0; text-decoration: none; }"
                + "a:hover { text-decoration: underline; }"
                + ".grid-container { display: flex; gap: 20px; margin: 20px 0; }"
                + ".grid-section { flex: 1; }"
                + ".grid-table { width: 100%; border-collapse: collapse; border: 1px solid #ddd; }"
                + ".grid-table th { background-color: #f5f5f5; padding: 10px; text-align: left; border: 1px solid #ddd; font-weight: bold; }"
                + ".grid-table td { padding: 10px; border: 1px solid #ddd; }"
                + ".grid-table tr:nth-child(even) { background-color: #fafafa; }"
                + ".instructions { background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; }"
                + ".highlight { padding: 10px; border-radius: 5px; margin: 15px 0; text-align: center; }"
                + ".highlight p { color: #d32f2f; font-weight: bold; }"
                + "</style>"
                + "</head><body>"

                + "<h3>" + applicationType + " Request Notification</h3>"

                + "<p><b>Program Aligned:</b> " + dto.getProgramAlignment() + "</p>"

                + "<p>You have received a new request with the following details:</p>"

                + "<div class='grid-container'>"

                // Request Details Section
                + "<div class='grid-section'>"
                + "<h4>Request Details</h4>"
                + "<table class='grid-table'>"
                + "<tr><td><b>Requester</b></td><td>" + requestDto.getLdap() + "</td></tr>"
                + "<tr><td><b>Leave Duration</b></td><td>" + requestDto.getStartDate() + " - " + requestDto.getEndDate() + "</td></tr>"
                + "<tr><td><b>Request Type</b></td><td>" + requestFor + "</td></tr>"
                + "<tr><td><b>Request Duration</b></td><td>" + duration + "</td></tr>"
                + "<tr><td><b>" + extraSectionTitle + "</b></td><td>" + requestReason + "</td></tr>"
                + "<tr><td><b>Org Timesheet SS</b></td><td><a href='" + timesheetLink + "'>" + timesheetLink + "</a></td></tr>"
                + "<tr><td><b>Calendar OOO SS</b></td><td><a href='" + oooLink + "'>" + oooLink + "</a></td></tr>"
                + "</table>"
                + "</div>"

                // Leave Balances Section
                + "<div class='grid-section'>"
                + "<h4>Leave Balances and WFH Summary</h4>"
                + "<table class='grid-table'>"
                + "<tr><td><b>Sick Leave (SL)</b></td><td>" + slBalance + "</td></tr>"
                + "<tr><td><b>Casual Leave (CL)</b></td><td>" + clBalance + "</td></tr>"
                + "<tr><td><b>Earned Leave (EL)</b></td><td>" + elBalance + "</td></tr>"
                + "<tr><td><b>Total Leave Balance</b></td><td>" + totalBalance + "</td></tr>"
                + "<tr><td><b>Total Leave Taken in Current Quarter</b></td><td>" + totalLeavesTakenThisQuarter + "</td></tr>"
                + "<tr><td><b>Total WFH Taken in Current Quarter</b></td><td>" + totalWFHQuaterly + "</td></tr>"
                + "</table>"
                + "</div>"

                + "</div>" // End grid-container

                + "<div class='instructions'>"
                + "<p><b>NOTE:</b> Please approve or deny this request via Teamsphere.</p>"
                + (isWFHMoreThan3Days ?
                    "<p><b>SPECIAL NOTICE:</b> This WFH request is for more than 3 days and has been redirected to the Account Manager for approval.</p>" :
                    "<p><b>WFH Requests</b> can only be approved by manager.</p>")
                + "<p><b>Leave Requests</b> can be approved by any leads involved in this mail.</p>"
                + "</div>"

                + "<div class='highlight'>"
                + "<p>PLEASE IGNORE THIS MAIL</p>"
                + "</div>"

                + "</body></html>";
    }


}
