<div class="attendance-container">
  <!-- Today's Attendance Check-in Section -->
  <div class="today-attendance" *ngIf="canCheckIn()">
    <h2>Today's Attendance</h2>
    <p>{{ currentDate }}</p>

    <mat-card>
      <mat-card-header>
        <mat-card-title class="greeting">
          <span class="hello-static">Hello,</span>
          <span class="name-animated">{{userInfo.name}}</span>
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <p>Record your arrival time</p>

        <div class="check-in-status">
          <button mat-raised-button color="primary" (click)="checkIn()" [disabled]="isUserCheckedInToday"
            *ngIf="!isUserCheckedInToday; else checkedInTemplate">
            Check In
          </button>

          <ng-template #checkedInTemplate>
            <div class="checked-in-badge">
              <mat-icon class="success-icon">check_circle</mat-icon>
              <span>{{ checkInStatusDisplay }}</span>
            </div>
          </ng-template>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Attendance Records Section -->
  <div class="attendance-records">
    <div class="actions-toolbar">
      <div class="action-group">
        <button mat-raised-button color="primary" class="action-button download-button" (click)="downloadCSV()"
          matTooltip="Download attendance as CSV">
          <mat-icon>download</mat-icon> Download CSV
        </button>
      </div>

      <div class="action-group">
        <button mat-raised-button color="primary" class="action-button toggle-columns-button" (click)="showColumnToggle = !showColumnToggle"
          matTooltip="Toggle columns">
          <mat-icon>view_column</mat-icon> Toggle Columns
        </button>
      </div>

      <div class="action-group">
        <mat-checkbox [(ngModel)]="showColumnFilters" class="filter-toggle">
          Show Column Filters
        </mat-checkbox>
      </div>

      <div class="action-group date-range-group">
        <mat-form-field appearance="outline" class="date-range-field">
          <mat-label>Date Range</mat-label>
          <mat-date-range-input [formGroup]="dateRange" [rangePicker]="picker">
            <input matStartDate formControlName="start" placeholder="Start date">
            <input matEndDate formControlName="end" placeholder="End date">
          </mat-date-range-input>
          <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
          <mat-date-range-picker #picker></mat-date-range-picker>
        </mat-form-field>
        <button mat-raised-button color="primary" (click)="applyDateFilter()" class="apply-filter-btn">
          <mat-icon>filter_list</mat-icon> Apply
        </button>
      </div>
    </div>

    <!-- Column Toggle Overlay -->
    <div *ngIf="showColumnToggle" class="column-toggle-overlay" (click)="showColumnToggle = false"></div>
    
    <!-- Column Toggle Dialog -->
    <div *ngIf="showColumnToggle" class="column-toggle-dialog">
      <div class="column-toggle-header">
        <h3>Toggle Columns</h3>
        <mat-form-field appearance="outline" class="column-search">
          <mat-label>Search columns</mat-label>
          <input matInput [(ngModel)]="columnSearchText" placeholder="Search columns...">
        </mat-form-field>
      </div>
      
      <div class="column-toggle-content">
        <mat-checkbox [checked]="allColumnsSelected" (change)="toggleAllColumns($event.checked)">
          Select All ({{ getFilteredColumns().length }} columns)
        </mat-checkbox>
        
        <div class="column-list">
          <mat-checkbox *ngFor="let column of getFilteredColumns()"
            [checked]="isColumnDisplayed(column)"
            (change)="toggleColumn(column)">
            {{ columnDisplayNames[column] }}
          </mat-checkbox>
        </div>
      </div>
      
      <div class="column-toggle-footer">
        <button mat-button (click)="showColumnToggle = false">Close</button>
      </div>
    </div>

    <div class="table-responsive">
      <mat-table [dataSource]="dataSource" matSort class="mat-elevation-z8">
        <!-- ID Column -->
        <ng-container matColumnDef="id">
          <mat-header-cell *matHeaderCellDef mat-sort-header sticky class="id-header" (mousedown)="startResizing($event, 'id')">
            <div class="header-container">
              <span class="header-text">ID</span>
              <button mat-icon-button #idTrigger="matMenuTrigger" *ngIf="showColumnFilters"
                [matMenuTriggerFor]="filterMenu" (menuOpened)="openFilterMenu('id', idTrigger)"
                [color]="isFilterActive('id') ? 'accent' : ''" (click)="$event.stopPropagation()">
                <mat-icon>filter_list</mat-icon>
              </button>
            </div>
          </mat-header-cell>
          <mat-cell *matCellDef="let record" class="id-cell">{{ record.id }}</mat-cell>
        </ng-container>

        <!-- LDAP Column -->
        <ng-container matColumnDef="ldap">
          <mat-header-cell *matHeaderCellDef mat-sort-header sticky class="ldap-header" (mousedown)="startResizing($event, 'ldap')">
            <div class="header-container">
              <span class="header-text">LDAP</span>
              <button mat-icon-button #ldapTrigger="matMenuTrigger" *ngIf="showColumnFilters"
                [matMenuTriggerFor]="filterMenu" (menuOpened)="openFilterMenu('ldap', ldapTrigger)"
                [color]="isFilterActive('ldap') ? 'accent' : ''" (click)="$event.stopPropagation()">
                <mat-icon>filter_list</mat-icon>
              </button>
            </div>
          </mat-header-cell>
          <mat-cell *matCellDef="let record" class="ldap-cell">{{ record.ldap }}</mat-cell>
        </ng-container>

        <!-- Name Column -->
        <ng-container matColumnDef="name">
          <mat-header-cell *matHeaderCellDef mat-sort-header class="name-header" (mousedown)="startResizing($event, 'name')">
            <div class="header-container">
              <span class="header-text">Name</span>
              <button mat-icon-button #nameTrigger="matMenuTrigger" *ngIf="showColumnFilters"
                [matMenuTriggerFor]="filterMenu" (menuOpened)="openFilterMenu('name', nameTrigger)"
                [color]="isFilterActive('name') ? 'accent' : ''" (click)="$event.stopPropagation()">
                <mat-icon>filter_list</mat-icon>
              </button>
            </div>
          </mat-header-cell>
          <mat-cell *matCellDef="let record" class="name-cell">{{ record.name }}</mat-cell>
        </ng-container>

        <!-- Team Column -->
        <ng-container matColumnDef="team">
          <mat-header-cell *matHeaderCellDef mat-sort-header class="team-header" (mousedown)="startResizing($event, 'team')">
            <div class="header-container">
              <span class="header-text">Team</span>
              <button mat-icon-button #checkInTrigger="matMenuTrigger" *ngIf="showColumnFilters"
                [matMenuTriggerFor]="filterMenu" (menuOpened)="openFilterMenu('team', checkInTrigger)"
                [color]="isFilterActive('team') ? 'accent' : ''" (click)="$event.stopPropagation()">
                <mat-icon>filter_list</mat-icon>
              </button>
            </div>
          </mat-header-cell>
          <mat-cell *matCellDef="let record" class="team-cell">{{ record.team }}</mat-cell>
        </ng-container>

        <!-- Date Column -->
        <ng-container matColumnDef="date">
          <mat-header-cell *matHeaderCellDef mat-sort-header class="date-header" (mousedown)="startResizing($event, 'date')">
            <div class="header-container">
              <span class="header-text">Date</span>
              <button mat-icon-button #dateTrigger="matMenuTrigger" *ngIf="showColumnFilters"
                [matMenuTriggerFor]="filterMenu" (menuOpened)="openFilterMenu('date', dateTrigger)"
                [color]="isFilterActive('date') ? 'accent' : ''" (click)="$event.stopPropagation()">
                <mat-icon>filter_list</mat-icon>
              </button>
            </div>
          </mat-header-cell>
          <mat-cell *matCellDef="let record" class="date-cell">{{ record.date }}</mat-cell>
        </ng-container>

        <!-- Check-In Column -->
        <ng-container matColumnDef="checkIn">
          <mat-header-cell *matHeaderCellDef mat-sort-header class="checkIn-header" (mousedown)="startResizing($event, 'checkIn')">
            <div class="header-container">
              <span class="header-text">Check-In Time</span>
              <button mat-icon-button #checkInTrigger="matMenuTrigger" *ngIf="showColumnFilters"
                [matMenuTriggerFor]="filterMenu" (menuOpened)="openFilterMenu('checkIn', checkInTrigger)"
                [color]="isFilterActive('checkIn') ? 'accent' : ''" (click)="$event.stopPropagation()">
                <mat-icon>filter_list</mat-icon>
              </button>
            </div>
          </mat-header-cell>
          <mat-cell *matCellDef="let record" class="checkIn-cell">{{ record.checkIn }}</mat-cell>
        </ng-container>

        <!-- Status Column -->
        <ng-container matColumnDef="status">
          <mat-header-cell *matHeaderCellDef mat-sort-header>
            <div class="header-container">
              <span class="header-text">Status</span>
              <button mat-icon-button #statusTrigger="matMenuTrigger" *ngIf="showColumnFilters"
                [matMenuTriggerFor]="filterMenu" (menuOpened)="openFilterMenu('status', statusTrigger)"
                [color]="isFilterActive('status') ? 'accent' : ''" (click)="$event.stopPropagation()">
                <mat-icon>filter_list</mat-icon>
              </button>
            </div>
          </mat-header-cell>
          <mat-cell *matCellDef="let record">
            <span class="status-badge" [class.late]="record.status?.toUpperCase() === 'LATE'"
              [class.on-time]="record.status?.toUpperCase() === 'ON TIME'">
              {{ record.status }}
            </span>
          </mat-cell>
        </ng-container>

        <!-- Reason Column -->
        <ng-container matColumnDef="reason">
          <mat-header-cell *matHeaderCellDef mat-sort-header class="reason-header" (mousedown)="startResizing($event, 'reason')">
            <div class="header-container">
              <span class="header-text">Reason</span>
              <button mat-icon-button #reasonTrigger="matMenuTrigger" *ngIf="showColumnFilters"
                [matMenuTriggerFor]="filterMenu" (menuOpened)="openFilterMenu('reason', reasonTrigger)"
                [color]="isFilterActive('reason') ? 'accent' : ''" (click)="$event.stopPropagation()">
                <mat-icon>filter_list</mat-icon>
              </button>
            </div>
          </mat-header-cell>
          <mat-cell *matCellDef="let record" class="reason-cell">{{ record.reason }}</mat-cell>
        </ng-container>

        <ng-container matColumnDef="comment">
          <mat-header-cell *matHeaderCellDef mat-sort-header class="comment-header" (mousedown)="startResizing($event, 'comment')">
            <div class="header-container">
              <span class="header-text">Additional Notes</span>
              <button mat-icon-button #reasonTrigger="matMenuTrigger" *ngIf="showColumnFilters"
                [matMenuTriggerFor]="filterMenu" (menuOpened)="openFilterMenu('comment', reasonTrigger)"
                [color]="isFilterActive('comment') ? 'accent' : ''" (click)="$event.stopPropagation()">
                <mat-icon>filter_list</mat-icon>
              </button>
            </div>
          </mat-header-cell>
          <mat-cell *matCellDef="let record" class="comment-cell">{{ record.comment }}</mat-cell>
        </ng-container>

        <!-- Location Column -->
        <ng-container matColumnDef="isOutsideOffice">
          <mat-header-cell *matHeaderCellDef mat-sort-header class="isOutsideOffice-header" (mousedown)="startResizing($event, 'isOutsideOffice')">
            <div class="header-container">
              <span class="header-text">Location</span>
              <button mat-icon-button #locationTrigger="matMenuTrigger" *ngIf="showColumnFilters"
                [matMenuTriggerFor]="filterMenu" (menuOpened)="openFilterMenu('isOutsideOffice', locationTrigger)"
                [color]="isFilterActive('isOutsideOffice') ? 'accent' : ''" (click)="$event.stopPropagation()">
                <mat-icon>filter_list</mat-icon>
              </button>
            </div>
          </mat-header-cell>
          <mat-cell *matCellDef="let record" class="isOutsideOffice-cell">
            <span class="location-badge {{record.isOutsideOffice ? 'outside' : 'inside'}}">
              <mat-icon>{{ record.isOutsideOffice ? 'location_off' : 'location_on' }}</mat-icon>
              {{ record.isOutsideOffice ? 'Outside' : 'Inside' }}
            </span>
          </mat-cell>
        </ng-container>

        <!-- Defaulter Column -->
        <ng-container matColumnDef="isDefaulter">
          <mat-header-cell *matHeaderCellDef mat-sort-header class="isDefaulter-header" (mousedown)="startResizing($event, 'isDefaulter')">
            <div class="header-container">
              <span class="header-text">Defaulter</span>
              <button mat-icon-button #defaulterTrigger="matMenuTrigger" *ngIf="showColumnFilters"
                [matMenuTriggerFor]="filterMenu" (menuOpened)="openFilterMenu('isDefaulter', defaulterTrigger)"
                [color]="isFilterActive('isDefaulter') ? 'accent' : ''" (click)="$event.stopPropagation()">
                <mat-icon>filter_list</mat-icon>
              </button>
            </div>
          </mat-header-cell>
          <mat-cell *matCellDef="let record" class="isDefaulter-cell">
            <span class="compliance-badge {{record.isDefaulter ? 'non-compliant' : 'compliant'}}">
              {{ record.isDefaulter ? 'Yes' : 'No' }}
            </span>
          </mat-cell>
        </ng-container>

        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
      </mat-table>

      <mat-paginator #paginator [pageSizeOptions]="[5, 10, 25, 50]" showFirstLastButtons>
      </mat-paginator>
    </div>
  </div>
</div>

<!-- Filter Menu Template -->
<mat-menu #filterMenu="matMenu" class="filter-menu">
  <div class="filter-menu-content" (click)="$event.stopPropagation()">
    <ng-container *ngIf="currentFilterMenuState.columnKey">
      <!-- Search Input -->
      <mat-form-field appearance="outline" class="filter-search">
        <mat-label>Search</mat-label>
        <input matInput [(ngModel)]="currentFilterMenuState.searchText" placeholder="Search options">
      </mat-form-field>

      <!-- Select All Checkbox -->
      <mat-checkbox [checked]="isAllTempSelected()" [indeterminate]="isSomeTempSelected()"
        (change)="toggleSelectAllTemp($event.checked)">
        Select All ({{ getUniqueColumnValues(currentFilterMenuState.columnKey).length }} items)
      </mat-checkbox>
      <hr>

      <!-- Filter Options -->
      <div style="max-height: 200px; overflow-y: auto;">
        <mat-checkbox *ngFor="let value of filteredMenuOptions" [checked]="isTempSelected(value)"
          (change)="toggleTempSelection(value, $event.checked)">
          {{ value }}
        </mat-checkbox>
      </div>
      <hr>

      <!-- Action Buttons -->
      <div style="display: flex; justify-content: space-between; margin-top: 10px;">
        <button mat-button (click)="onFilterApplied()">Apply</button>
        <button mat-button (click)="clearColumnFilter()">Clear</button>
      </div>
    </ng-container>
  </div>
</mat-menu>