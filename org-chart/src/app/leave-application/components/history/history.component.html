<!-- history.component.html -->
<div class="history-dashboard">
    <!-- Action Bar -->
    <div class="actions">
        <button mat-raised-button color="primary" class="action-button download-button" (click)="downloadCSV()">
            <mat-icon>download</mat-icon> Download CSV
        </button>

        <mat-menu #columnMenu="matMenu" class="column-menu">
            <mat-divider></mat-divider>
            <!-- Search input -->
            <mat-form-field appearance="outline" class="column-search-field">
                <mat-label>Search columns</mat-label>
                <input matInput [(ngModel)]="columnSearchText" placeholder="Search columns">
                <button *ngIf="columnSearchText" matSuffix mat-icon-button aria-label="Clear"
                    (click)="columnSearchText=''">
                    <mat-icon>close</mat-icon>
                </button>
            </mat-form-field>


            <mat-divider></mat-divider>

            <!-- Column checkboxes -->
            <div class="column-menu-items">
                <mat-checkbox *ngFor="let column of getFilteredColumns()" [checked]="isColumnDisplayed(column)"
                    (change)="toggleColumn(column)" [disabled]="column === 'actions'" class="column-toggle-checkbox">
                    {{ columnDisplayNames[column] }}
                </mat-checkbox>
            </div>
        </mat-menu>

        <mat-form-field appearance="outline">
            <mat-label>Search</mat-label>
            <mat-icon matPrefix>search</mat-icon>
            <input matInput (keyup)="applyGlobalFilter($event)" placeholder="Search entries">
        </mat-form-field>
    </div>

    <!-- Table -->
    <div class="table-responsive">
        <mat-table [dataSource]="dataSource" matSort class="mat-elevation-z8">

            <!-- LDAP -->
            <ng-container matColumnDef="ldap">
                <mat-header-cell *matHeaderCellDef mat-sort-header>LDAP</mat-header-cell>
                <mat-cell *matCellDef="let row">{{ row.ldap }}</mat-cell>
            </ng-container>

            <ng-container matColumnDef="applicationType">
                <mat-header-cell *matHeaderCellDef mat-sort-header>Application Type</mat-header-cell>
                <mat-cell *matCellDef="let row">{{ row.applicationType }}</mat-cell>
            </ng-container>

            <!-- Leave Type -->
            <ng-container matColumnDef="leaveType">
                <mat-header-cell *matHeaderCellDef mat-sort-header>Leave Type</mat-header-cell>
                <mat-cell *matCellDef="let row">{{ row.leaveType }}</mat-cell>
            </ng-container>

            <ng-container matColumnDef="shiftCodeAtRequestTime">
                <mat-header-cell *matHeaderCellDef mat-sort-header>Shift</mat-header-cell>
                <mat-cell *matCellDef="let row">{{ row.shiftCodeAtRequestTime }}</mat-cell>
            </ng-container>

            <ng-container matColumnDef="duration">
                <mat-header-cell *matHeaderCellDef mat-sort-header>Duration</mat-header-cell>
                <mat-cell *matCellDef="let row">{{ row.duration }}</mat-cell>
            </ng-container>

            <!-- Start Date -->
            <ng-container matColumnDef="startDate">
                <mat-header-cell *matHeaderCellDef mat-sort-header>Start Date</mat-header-cell>
                <mat-cell *matCellDef="let row">{{ row.startDate | date:'MMM d, y' }}</mat-cell>
            </ng-container>

            <ng-container matColumnDef="startTime">
                <mat-header-cell *matHeaderCellDef mat-sort-header>Start Time</mat-header-cell>
                <mat-cell *matCellDef="let row">{{ row.startTime }}</mat-cell>
            </ng-container>

            <!-- End Date -->
            <ng-container matColumnDef="endDate">
                <mat-header-cell *matHeaderCellDef mat-sort-header>End Date</mat-header-cell>
                <mat-cell *matCellDef="let row">{{ row.endDate | date:'MMM d, y' }}</mat-cell>
            </ng-container>

            <ng-container matColumnDef="endTime">
                <mat-header-cell *matHeaderCellDef mat-sort-header>End Time</mat-header-cell>
                <mat-cell *matCellDef="let row">{{ row.endTime }}</mat-cell>
            </ng-container>

            <ng-container matColumnDef="timesheetProof">
                <mat-header-cell *matHeaderCellDef mat-sort-header>TimeSheet SS</mat-header-cell>
                <mat-cell *matCellDef="let row">{{ row.timesheetProof }}</mat-cell>
            </ng-container>

            <ng-container matColumnDef="oooProof">
                <mat-header-cell *matHeaderCellDef mat-sort-header>OOO SS</mat-header-cell>
                <mat-cell *matCellDef="let row">{{ row.oooProof }}</mat-cell>
            </ng-container>

            <ng-container matColumnDef="reason">
                <mat-header-cell *matHeaderCellDef mat-sort-header>Reason</mat-header-cell>
                <mat-cell *matCellDef="let row">{{ row.reason }}</mat-cell>
            </ng-container>

            <ng-container matColumnDef="backupInfo">
                <mat-header-cell *matHeaderCellDef mat-sort-header>Backup Info</mat-header-cell>
                <mat-cell *matCellDef="let row">{{ row.backupInfo }}</mat-cell>
            </ng-container>

            <ng-container matColumnDef="documentPath">
                <mat-header-cell *matHeaderCellDef mat-sort-header>Document</mat-header-cell>
                <mat-cell *matCellDef="let row">
                    <ng-container *ngIf="row.documentPath && row.documentPath !== 'NA'; else noDoc">
                        <button mat-icon-button (click)="openPreviewDialog(row)">
                            <mat-icon>visibility</mat-icon>
                        </button>
                    </ng-container>
                    <ng-template #noDoc>
                        <span>N/A</span>
                    </ng-template>
                </mat-cell>
            </ng-container>

            <!-- Status -->
            <ng-container matColumnDef="status">
                <mat-header-cell *matHeaderCellDef mat-sort-header>Status</mat-header-cell>
                <mat-cell *matCellDef="let row">
                    <span [ngClass]="{
              'status-pending': row.status === 'PENDING',
              'status-approved': row.status === 'APPROVED',
              'status-rejected': row.status === 'DELETED'
            }">{{ row.status }}</span>
                </mat-cell>
            </ng-container>

            <!-- Actions -->
            <ng-container matColumnDef="actions">
                <mat-header-cell *matHeaderCellDef>Actions</mat-header-cell>
                <mat-cell *matCellDef="let row">
                    <!-- Edit -->
                    <!-- Edit -->
                    <button mat-raised-button color="primary" class="action-button edit-button"
                        [disabled]="row.status !== 'PENDING'" (click)="editRequest(row)"
                        [matTooltip]="row.status !== 'PENDING' ? 'Only pending requests can be edited' : 'Edit request'"
                        [matTooltipDisabled]="row.status === 'PENDING'">
                        Edit
                    </button>

                    <!-- Delete -->
                    <button mat-raised-button color="warn" class="action-button delete-button"
                        [disabled]="row.status !== 'PENDING'" (click)="deleteRequest(row)"
                        [matTooltip]="row.status !== 'PENDING' ? 'Only pending requests can be deleted' : 'Delete request'"
                        [matTooltipDisabled]="row.status === 'PENDING'">
                        Delete
                    </button>

                </mat-cell>
            </ng-container>


            <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
            <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
        </mat-table>
        <mat-paginator #paginator [pageSizeOptions]="[5, 10, 25, 50]" showFirstLastButtons>
        </mat-paginator>
    </div>
</div>