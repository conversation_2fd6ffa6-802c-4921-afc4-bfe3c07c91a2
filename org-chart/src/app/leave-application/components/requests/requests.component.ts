import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { LeaveService } from 'src/app/services/leave.service';
import { NotificationService } from 'src/app/shared/notification.service';
import { MatDialog } from '@angular/material/dialog';
import { DocumentPreviewDialogComponent } from './document-preview-dialog/document-preview-dialog.component';
import { MatButtonToggleChange } from '@angular/material/button-toggle';

@Component({
  selector: 'app-requests',
  templateUrl: './requests.component.html',
  styleUrls: ['./requests.component.css']
})
export class RequestsComponent implements OnInit {
  displayedColumns: string[] = [
    'ldap', 'name', 'requestType', 'leaveType',
    'startDate', 'endDate', 'duration', 'status', 'reason', 'document', 'actions'
  ];
  userRole: string = '';
  userName: string = '';
  processedDataSource: MatTableDataSource<any> = new MatTableDataSource();
  processedRequests: any[] = []; // Raw data


  // Tab DataSources
  pendingDataSource: MatTableDataSource<any> = new MatTableDataSource();
  processedApprovedDataSource = new MatTableDataSource<any>();
  processedRejectedDataSource = new MatTableDataSource<any>();
  processedRevokedDataSource = new MatTableDataSource<any>();

  // Filters
  processedStatuses: string[] = ['APPROVED', 'REJECTED', 'REVOKED'];
  selectedStatus: string = '';

  // ViewChilds
  @ViewChild('pendingPaginator') pendingPaginator!: MatPaginator;
  @ViewChild('processedPaginator') processedPaginator!: MatPaginator;
  @ViewChild('approvedPaginator') approvedPaginator!: MatPaginator;
  @ViewChild('rejectedPaginator') rejectedPaginator!: MatPaginator;
  @ViewChild('revokedPaginator') revokedPaginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private leaveService: LeaveService,
    private notificationService: NotificationService,
    private dialog: MatDialog,
  ) { }

  ngOnInit(): void {
    this.userRole = (localStorage.getItem('role') || '').toUpperCase();
    this.userName = (localStorage.getItem('username') || '').toLowerCase();
    this.fetchPendingRequests();
    this.fetchProcessedRequests();
  }


  ngAfterViewInit(): void {
    if (this.processedApprovedDataSource) {
      this.processedApprovedDataSource.paginator = this.approvedPaginator;
      this.processedApprovedDataSource.sort = this.sort;
    }
    if (this.processedRejectedDataSource) {
      this.processedRejectedDataSource.paginator = this.rejectedPaginator;
      this.processedRejectedDataSource.sort = this.sort;
    }
    if (this.processedRevokedDataSource) {
      this.processedRevokedDataSource.paginator = this.revokedPaginator;
      this.processedRevokedDataSource.sort = this.sort;
    }

    this.pendingDataSource.paginator = this.pendingPaginator;
    this.pendingDataSource.sort = this.sort;
  }

  openPreviewDialog(row: any): void {
    const baseUrl = 'https://teamsphere.in/'; // Use for Main Production

   // const baseUrl = 'http://localhost:8080/';  URL for Development Purposes
  
    const documentPath = row.documentPath || row.document || null;
  
    this.dialog.open(DocumentPreviewDialogComponent, {
      width: '600px',
      data: {
        reason: row.reason,
        documentPath: documentPath ? baseUrl + documentPath : null
      }
    });
  }
  

  fetchPendingRequests(): void {
    this.leaveService.getPendingRequestsForLead().subscribe({
      next: (requests) => {
        this.pendingDataSource = new MatTableDataSource(requests);
        this.pendingDataSource.paginator = this.pendingPaginator;
        this.pendingDataSource.sort = this.sort;
      },
      error: () => {
        this.notificationService.showNotification({
          type: 'error',
          message: 'Failed to fetch pending requests.'
        });
      }
    });
  }

  canApprove(request: any): boolean {
    const isLead = this.userRole === 'LEAD';
    const isWFH = request.applicationType === 'Work From Home';
    const isDsheoran = this.userName === 'dsheoran';

    // Compute duration in days using start and end dates
    const start = new Date(request.startDate);
    const end = new Date(request.endDate);
    const durationInDays = Math.floor((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;

    const isLongWFH = durationInDays >= 3;

    // Leads cannot approve any WFH
    if (isLead && isWFH) return false;

    // Only dsheoran can approve WFH of 3+ days
    if (isWFH && isLongWFH && !isDsheoran) return false;

    return true;
  }



  fetchProcessedRequests(): void {
    this.leaveService.getProcessedRequestsForLead().subscribe({
      next: (requests) => {
        this.processedRequests = requests || [];

        this.processedApprovedDataSource.data = this.processedRequests.filter(r => r.status === 'APPROVED');
        this.processedRejectedDataSource.data = this.processedRequests.filter(r => r.status === 'REJECTED');
        this.processedRevokedDataSource.data = this.processedRequests.filter(r => r.status === 'REVOKED');

        this.selectedStatus = 'APPROVED';
        setTimeout(() => this.updatePaginatorAndSort(), 0);
      },
      error: () => {
        this.notificationService.showNotification({
          type: 'error',
          message: 'Failed to fetch processed requests.'
        });
      }
    });
  }

  updatePaginatorAndSort(): void {
    switch (this.selectedStatus) {
      case 'APPROVED':
        this.processedApprovedDataSource.paginator = this.approvedPaginator;
        this.processedApprovedDataSource.sort = this.sort;
        break;
      case 'REJECTED':
        this.processedRejectedDataSource.paginator = this.rejectedPaginator;
        this.processedRejectedDataSource.sort = this.sort;
        break;
      case 'REVOKED':
        this.processedRevokedDataSource.paginator = this.revokedPaginator;
        this.processedRevokedDataSource.sort = this.sort;
        break;
    }
  }

  getProcessedDataSource(): MatTableDataSource<any> {
    switch (this.selectedStatus) {
      case 'APPROVED': return this.processedApprovedDataSource;
      case 'REJECTED': return this.processedRejectedDataSource;
      case 'REVOKED': return this.processedRevokedDataSource;
      default: return new MatTableDataSource();
    }
  }

  approveRequest(request: any): void {
    this.leaveService.approveRequestVunno(request).subscribe({
      next: () => {
        this.notificationService.showNotification({
          type: 'success',
          message: 'Request approved.'
        });
        this.fetchPendingRequests();
        this.fetchProcessedRequests(); // also update processed
      },
      error: (error) => {
        const errorMessage = error?.error?.message || 'Failed to approve request.';
        this.notificationService.showNotification({
          type: 'error',
          message: errorMessage
        });
      }
    });
  }

  rejectRequest(request: any): void {
    this.leaveService.rejectRequestVunno(request).subscribe({
      next: () => {
        this.notificationService.showNotification({
          type: 'success',
          message: 'Request rejected.'
        });
        this.fetchPendingRequests();
        this.fetchProcessedRequests();
      },
      error: (error) => {
        const errorMessage = error?.error?.message || 'Failed to reject request.';
        this.notificationService.showNotification({
          type: 'error',
          message: errorMessage
        });
      }
    });
  }

  revokeRequest(request: any): void {
    this.leaveService.revokeRequestVunno(request).subscribe({
      next: () => {
        this.notificationService.showNotification({
          type: 'success',
          message: 'Request revoked.'
        });
        this.fetchProcessedRequests();
      },
      error: (error) => {
        const errorMessage = error?.error?.message || 'Failed to revoke request.';
        this.notificationService.showNotification({
          type: 'error',
          message: errorMessage
        });
      }
    });
  }

  onStatusChange(event: MatButtonToggleChange): void {
    this.selectedStatus = event.value;
    setTimeout(() => this.updatePaginatorAndSort(), 0); // fixes paginator not binding
  }

  isPastEndDate(endDate: string): boolean {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const leaveEndDate = new Date(endDate);
    leaveEndDate.setHours(0, 0, 0, 0);
    return leaveEndDate < today;
  }
}

