<mat-tab-group>
  <mat-tab label="Processed Requests">
    <div class="processed-requests-container">
      <mat-button-toggle-group class="toggle-group" [value]="selectedStatus" appearance="legacy" exclusive
        (change)="onStatusChange($event)">
        <mat-button-toggle *ngFor="let status of processedStatuses" [value]="status">
          {{ status }}
        </mat-button-toggle>
      </mat-button-toggle-group>

      <div class="table-wrapper">
        <mat-table [dataSource]="getProcessedDataSource()" matSort class="mat-elevation-z8">
          <!-- (change)="onStatusChange($event)" -->
          <ng-container matColumnDef="ldap">
            <mat-header-cell *matHeaderCellDef mat-sort-header>LDAP</mat-header-cell>
            <mat-cell *matCellDef="let row">{{ row.ldap }}</mat-cell>
          </ng-container>

          <ng-container matColumnDef="name">
            <mat-header-cell *matHeaderCellDef mat-sort-header>Name</mat-header-cell>
            <mat-cell *matCellDef="let row">{{ row.requestorName }}</mat-cell>
          </ng-container>

          <ng-container matColumnDef="requestType">
            <mat-header-cell *matHeaderCellDef mat-sort-header>Request Type</mat-header-cell>
            <mat-cell *matCellDef="let row">{{ row.applicationType }}</mat-cell>
          </ng-container>

          <ng-container matColumnDef="leaveType">
            <mat-header-cell *matHeaderCellDef mat-sort-header>Leave Type</mat-header-cell>
            <mat-cell *matCellDef="let row">{{ row.leaveType }}</mat-cell>
          </ng-container>

          <ng-container matColumnDef="startDate">
            <mat-header-cell *matHeaderCellDef mat-sort-header>Start Date</mat-header-cell>
            <mat-cell *matCellDef="let row">{{ row.startDate | date:'MMM d, y' }}</mat-cell>
          </ng-container>

          <ng-container matColumnDef="endDate">
            <mat-header-cell *matHeaderCellDef mat-sort-header>End Date</mat-header-cell>
            <mat-cell *matCellDef="let row">{{ row.endDate | date:'MMM d, y' }}</mat-cell>
          </ng-container>

          <ng-container matColumnDef="duration">
            <mat-header-cell *matHeaderCellDef mat-sort-header>Duration</mat-header-cell>
            <mat-cell *matCellDef="let row">{{ row.lvWfhDuration }}</mat-cell>
          </ng-container>

          <ng-container matColumnDef="reason">
            <mat-header-cell *matHeaderCellDef mat-sort-header>Reason</mat-header-cell>
            <mat-cell *matCellDef="let row">{{ row.reason || 'NA'}}</mat-cell>
          </ng-container>

          <ng-container matColumnDef="document">
            <mat-header-cell *matHeaderCellDef>Proof</mat-header-cell>
            <mat-cell *matCellDef="let row">
              <button *ngIf="row.document && row.document !== 'NA'" mat-icon-button (click)="openPreviewDialog(row)">
                <mat-icon>visibility</mat-icon>
              </button>
              <span *ngIf="!row.document || row.document === 'NA'">N/A</span>
            </mat-cell>
          </ng-container>


          <ng-container matColumnDef="status">
            <mat-header-cell *matHeaderCellDef mat-sort-header>Status</mat-header-cell>
            <mat-cell *matCellDef="let row">
              <span [ngClass]="{
            'status-approved': row.status === 'APPROVED',
            'status-rejected': row.status === 'REJECTED',
            'status-revoked': row.status === 'REVOKED'
          }">{{ row.status }}</span>
            </mat-cell>
          </ng-container>

          <ng-container matColumnDef="actions">
            <mat-header-cell *matHeaderCellDef>Actions</mat-header-cell>
            <mat-cell *matCellDef="let row">
              <button mat-raised-button color="warn" class="action-button revoke-button"
                [disabled]="isPastEndDate(row.endDate)" *ngIf="row.status === 'APPROVED'" (click)="revokeRequest(row)">
                Revoke
              </button>
            </mat-cell>
          </ng-container>

          <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
          <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
        </mat-table>
      </div>
      <mat-paginator [pageSize]="5" [pageSizeOptions]="[5, 10, 15,20]" [hidePageSize]="false"
        *ngIf="selectedStatus === 'APPROVED'" #approvedPaginator>
      </mat-paginator>

      <mat-paginator [pageSize]="5" [pageSizeOptions]="[5, 10, 15,20]" [hidePageSize]="false"
        *ngIf="selectedStatus === 'REJECTED'" #rejectedPaginator>
      </mat-paginator>

      <mat-paginator [pageSize]="5" [pageSizeOptions]="[5, 10, 15,20]" [hidePageSize]="false"
        *ngIf="selectedStatus === 'REVOKED'" #revokedPaginator>
      </mat-paginator>

    </div>
  </mat-tab>

  <mat-tab label="Pending Requests">
    <div class="new-requests-table">
      <mat-table [dataSource]="pendingDataSource" matSort class="mat-elevation-z8">

        <ng-container matColumnDef="ldap">
          <mat-header-cell *matHeaderCellDef mat-sort-header>LDAP</mat-header-cell>
          <mat-cell *matCellDef="let row">{{ row.ldap }}</mat-cell>
        </ng-container>

        <ng-container matColumnDef="name">
          <mat-header-cell *matHeaderCellDef mat-sort-header>Name</mat-header-cell>
          <mat-cell *matCellDef="let row">{{ row.requestorName }}</mat-cell>
        </ng-container>

        <ng-container matColumnDef="requestType">
          <mat-header-cell *matHeaderCellDef mat-sort-header>Request Type</mat-header-cell>
          <mat-cell *matCellDef="let row">{{ row.applicationType }}</mat-cell>
        </ng-container>

        <ng-container matColumnDef="leaveType">
          <mat-header-cell *matHeaderCellDef mat-sort-header>Leave Type</mat-header-cell>
          <mat-cell *matCellDef="let row">{{ row.leaveType }}</mat-cell>
        </ng-container>

        <ng-container matColumnDef="startDate">
          <mat-header-cell *matHeaderCellDef mat-sort-header>Start Date</mat-header-cell>
          <mat-cell *matCellDef="let row">{{ row.startDate | date:'MMM d, y' }}</mat-cell>
        </ng-container>

        <ng-container matColumnDef="endDate">
          <mat-header-cell *matHeaderCellDef mat-sort-header>End Date</mat-header-cell>
          <mat-cell *matCellDef="let row">{{ row.endDate | date:'MMM d, y' }}</mat-cell>
        </ng-container>

        <ng-container matColumnDef="duration">
          <mat-header-cell *matHeaderCellDef mat-sort-header>Duration</mat-header-cell>
          <mat-cell *matCellDef="let row">{{ row.lvWfhDuration }}</mat-cell>
        </ng-container>

        <ng-container matColumnDef="reason">
          <mat-header-cell *matHeaderCellDef mat-sort-header>Reason</mat-header-cell>
          <mat-cell *matCellDef="let row">{{ row.reason || 'NA'}}</mat-cell>
        </ng-container>

        <ng-container matColumnDef="document">
          <mat-header-cell *matHeaderCellDef>Proof</mat-header-cell>
          <mat-cell *matCellDef="let row">
            <button *ngIf="row.document && row.document !== 'NA'" mat-icon-button (click)="openPreviewDialog(row)">
              <mat-icon>visibility</mat-icon>
            </button>
            <span *ngIf="!row.document || row.document === 'NA'">N/A</span>
          </mat-cell>
        </ng-container>

        <ng-container matColumnDef="status">
          <mat-header-cell *matHeaderCellDef mat-sort-header>Status</mat-header-cell>
          <mat-cell *matCellDef="let row">
            <span [ngClass]="{
                'status-pending': row.status === 'PENDING',
                'status-approved': row.status === 'APPROVED',
                'status-rejected': row.status === 'REJECTED'
              }">{{ row.status }}</span>
          </mat-cell>
        </ng-container>

        <ng-container matColumnDef="actions">
          <mat-header-cell *matHeaderCellDef>Actions</mat-header-cell>
          <mat-cell *matCellDef="let row">
            <!-- <button mat-raised-button color="primary" class="action-button approve-button"
              *ngIf="row.status === 'PENDING' && canApprove(row)" (click)="approveRequest(row)">
              Approve
            </button> -->
            <button mat-raised-button color="primary" class="action-button approve-button"
              *ngIf="row.status === 'PENDING' && canApprove(row)" (click)="approveRequest(row)">
              Approve
            </button>

            <button mat-raised-button color="warn" class="action-button delete-button"
              *ngIf="row.status === 'PENDING' && canApprove(row)" (click)="rejectRequest(row)">
              Reject
            </button>

          </mat-cell>
        </ng-container>

        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
      </mat-table>

      <mat-paginator #pendingPaginator [pageSizeOptions]="[5,10, 20, 50]" showFirstLastButtons>
      </mat-paginator>
    </div>
  </mat-tab>
</mat-tab-group>