<div class="form-container">
  <app-page-header title="Attendance and Leave/Work from Home Application"
    subtitle="Apply for leave and work from home">
  </app-page-header>

  <!-- Tabs Section -->
  <div class="tabs">

    <div class="tab" [class.active]="activeTab === 'attendance'" (click)="setActiveTab('attendance')">
      Attendance
    </div>

    <div class="tab" [class.active]="activeTab === 'new'" (click)="setActiveTab('new')">
      New Request
    </div>

    <div class="tab" [class.active]="activeTab === 'balance'" (click)="setActiveTab('balance')">
      Balance
    </div>

    <div class="tab" [class.active]="activeTab === 'history'" (click)="setActiveTab('history')">
      History
    </div>

    <div class="tab" [class.active]="activeTab === 'requests'" (click)="setActiveTab('requests')"
      *ngIf="isRoleAllowed()">
      Requests
    </div>
    <!-- *ngIf="isUploadAllowed()" -->
  </div>

  <app-attendance [hidden]="activeTab !== 'attendance'"></app-attendance>

  <app-balance *ngIf="activeTab === 'balance'" [leaveBalance]="leaveBalance" [canUpload]="isRoleAllowed()"
    [userRole]="userRole">
  </app-balance>

  <app-history *ngIf="activeTab === 'history'" [userRole]="userRole" [ldap]="leaveForm.get('ldap')?.value">
  </app-history>

  <app-requests *ngIf="activeTab === 'requests'"></app-requests>


  <!-- New Request Tab Content -->
  <form [formGroup]="leaveForm" (ngSubmit)="onSubmit()" *ngIf="activeTab === 'new'" class="tab-content">
    <h2>Apply for Leave/WFH</h2>

    <!-- Common Fields -->
    <div class="form-row">
      <mat-form-field appearance="outline">
        <mat-label>Ldap</mat-label>
        <input matInput formControlName="ldap" (blur)="fetchManager()" readonly>
      </mat-form-field>

      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Approving Lead/Manager</mat-label>
        <input matInput formControlName="approver" readonly>
      </mat-form-field>
    </div>

    <div class="form-row">
      <mat-form-field appearance="outline">
        <mat-label>Request Type</mat-label>
        <mat-select formControlName="requestType" (selectionChange)="onRequestTypeChange()">
          <mat-option *ngFor="let option of requestTypeOptions" [value]="option.value">{{option.label}}</mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field *ngIf="!isWFH" appearance="outline" class="full-width">
        <mat-label>Leave Type</mat-label>
        <mat-select formControlName="leaveType" (selectionChange)="onRequestTypeChange()">
          <mat-option *ngFor="let option of leaveTypeOptions" [value]="option.value">{{option.label}}</mat-option>
        </mat-select>
      </mat-form-field>
      

      <div *ngIf="isWFH">
        <div class="form-row full-width">
          <label>Attach Supporting Document (Optional - [pdf,png,jpg,jpeg-Accepted])</label>
          <input type="file" accept=".pdf,.png,.jpg,.jpeg" (change)="onFileSelected($event)" />
        </div>
      </div>
    </div>

    <div class="form-row full-width">
      <mat-form-field appearance="outline" class="full-width-field">
        <mat-label>Duration</mat-label>
        <mat-select formControlName="durationType" (selectionChange)="onRequestTypeChange()">
          <mat-option *ngFor="let option of durationTypeOptions" [value]="option.value">{{option.label}}</mat-option>
        </mat-select>
      </mat-form-field>
    </div>

    <div *ngIf="!isWFH">
      <div class="form-row">
        <mat-form-field appearance="outline">
          <mat-label>OOO Screenshot Link</mat-label>
          <input matInput formControlName="oooProof">
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Timesheet Screenshot Link</mat-label>
          <input matInput formControlName="timesheetProof">
        </mat-form-field>
      </div>

      <div class="form-row full-width">
        <mat-form-field appearance="outline" class="full-width-field">
          <mat-label>Backup Info</mat-label>
          <textarea matInput formControlName="backupInfo" rows="4"></textarea>
        </mat-form-field>
      </div>
    </div>

    <!-- Common Fields -->

    <div class="form-row">
      <mat-form-field appearance="outline">
        <mat-label>Start Date</mat-label>
        <input matInput [matDatepicker]="startDatePicker" formControlName="startDate" readonly>
        <mat-datepicker-toggle matSuffix [for]="startDatePicker"></mat-datepicker-toggle>
        <mat-datepicker #startDatePicker></mat-datepicker>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>End Date</mat-label>
        <input matInput [matDatepicker]="endDatePicker" formControlName="endDate" readonly [disabled]="isEndDateDisabled">
        <mat-datepicker-toggle matSuffix [for]="endDatePicker" [disabled]="isEndDateDisabled"></mat-datepicker-toggle>
        <mat-datepicker #endDatePicker [disabled]="isEndDateDisabled"></mat-datepicker>
      </mat-form-field>
    </div>

    <div class="form-row">
      <mat-form-field appearance="outline">
        <mat-label>Start Time</mat-label>
        <input matInput type="time" formControlName="startTime">
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>End Time</mat-label>
        <input matInput type="time" formControlName="endTime">
      </mat-form-field>
    </div>

    <div *ngIf="isWFH" class="form-row full-width">
      <mat-form-field appearance="outline" class="full-width-field">
        <mat-label>Reason for Work From Home</mat-label>
        <textarea matInput formControlName="reason" rows="4"
          placeholder="Enter the reason for Work From Home"></textarea>
      </mat-form-field>
    </div>

    <!-- WFH Disclaimer -->
    <div *ngIf="showWFHDisclaimer" class="wfh-disclaimer">
      <mat-card class="disclaimer-card">
        <mat-card-content>
          <div class="disclaimer-header">
            <mat-icon color="warn">warning</mat-icon>
            <span class="disclaimer-title">Important Notice</span>
          </div>
          <p class="disclaimer-message">{{ wfhDisclaimerMessage }}</p>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Buttons -->
    <div class="buttons">
      <button mat-raised-button color="warn" class="action-button cancel-button" type="button"
        (click)="resetForm()">Cancel</button>
      <button mat-raised-button color="primary" class="action-button save-button" type="submit">Submit Request</button>
    </div>
  </form>
</div>